/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { createFileRoute } from '@tanstack/react-router'

import { Route as rootRouteImport } from './routes/__root'
import { Route as AuthenticatedRouteImport } from './routes/_authenticated'
import { Route as IndexRouteImport } from './routes/index'

const SignupLazyRouteImport = createFileRoute('/signup')()
const LoginLazyRouteImport = createFileRoute('/login')()
const AuthenticatedPagesLazyRouteImport = createFileRoute(
  '/_authenticated/pages',
)()
const AuthenticatedPageIdLazyRouteImport = createFileRoute(
  '/_authenticated/page/$id',
)()

const SignupLazyRoute = SignupLazyRouteImport.update({
  id: '/signup',
  path: '/signup',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() => import('./routes/signup.lazy').then((d) => d.Route))
const LoginLazyRoute = LoginLazyRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any).lazy(() => import('./routes/login.lazy').then((d) => d.Route))
const AuthenticatedRoute = AuthenticatedRouteImport.update({
  id: '/_authenticated',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthenticatedPagesLazyRoute = AuthenticatedPagesLazyRouteImport.update({
  id: '/pages',
  path: '/pages',
  getParentRoute: () => AuthenticatedRoute,
} as any).lazy(() =>
  import('./routes/_authenticated/pages.lazy').then((d) => d.Route),
)
const AuthenticatedPageIdLazyRoute = AuthenticatedPageIdLazyRouteImport.update({
  id: '/page/$id',
  path: '/page/$id',
  getParentRoute: () => AuthenticatedRoute,
} as any).lazy(() =>
  import('./routes/_authenticated/page.$id.lazy').then((d) => d.Route),
)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '/login': typeof LoginLazyRoute
  '/signup': typeof SignupLazyRoute
  '/pages': typeof AuthenticatedPagesLazyRoute
  '/page/$id': typeof AuthenticatedPageIdLazyRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '/login': typeof LoginLazyRoute
  '/signup': typeof SignupLazyRoute
  '/pages': typeof AuthenticatedPagesLazyRoute
  '/page/$id': typeof AuthenticatedPageIdLazyRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_authenticated': typeof AuthenticatedRouteWithChildren
  '/login': typeof LoginLazyRoute
  '/signup': typeof SignupLazyRoute
  '/_authenticated/pages': typeof AuthenticatedPagesLazyRoute
  '/_authenticated/page/$id': typeof AuthenticatedPageIdLazyRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths: '/' | '/login' | '/signup' | '/pages' | '/page/$id'
  fileRoutesByTo: FileRoutesByTo
  to: '/' | '/login' | '/signup' | '/pages' | '/page/$id'
  id:
    | '__root__'
    | '/'
    | '/_authenticated'
    | '/login'
    | '/signup'
    | '/_authenticated/pages'
    | '/_authenticated/page/$id'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthenticatedRoute: typeof AuthenticatedRouteWithChildren
  LoginLazyRoute: typeof LoginLazyRoute
  SignupLazyRoute: typeof SignupLazyRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/signup': {
      id: '/signup'
      path: '/signup'
      fullPath: '/signup'
      preLoaderRoute: typeof SignupLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginLazyRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated': {
      id: '/_authenticated'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthenticatedRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authenticated/pages': {
      id: '/_authenticated/pages'
      path: '/pages'
      fullPath: '/pages'
      preLoaderRoute: typeof AuthenticatedPagesLazyRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
    '/_authenticated/page/$id': {
      id: '/_authenticated/page/$id'
      path: '/page/$id'
      fullPath: '/page/$id'
      preLoaderRoute: typeof AuthenticatedPageIdLazyRouteImport
      parentRoute: typeof AuthenticatedRoute
    }
  }
}

interface AuthenticatedRouteChildren {
  AuthenticatedPagesLazyRoute: typeof AuthenticatedPagesLazyRoute
  AuthenticatedPageIdLazyRoute: typeof AuthenticatedPageIdLazyRoute
}

const AuthenticatedRouteChildren: AuthenticatedRouteChildren = {
  AuthenticatedPagesLazyRoute: AuthenticatedPagesLazyRoute,
  AuthenticatedPageIdLazyRoute: AuthenticatedPageIdLazyRoute,
}

const AuthenticatedRouteWithChildren = AuthenticatedRoute._addFileChildren(
  AuthenticatedRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthenticatedRoute: AuthenticatedRouteWithChildren,
  LoginLazyRoute: LoginLazyRoute,
  SignupLazyRoute: SignupLazyRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
